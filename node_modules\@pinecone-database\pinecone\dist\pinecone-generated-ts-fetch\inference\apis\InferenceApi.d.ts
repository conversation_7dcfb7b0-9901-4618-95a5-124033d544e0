/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import * as runtime from '../runtime';
import type { EmbedRequest, EmbeddingsList, ModelInfo, ModelInfoList, RerankRequest, RerankResult } from '../models/index';
export interface EmbedOperationRequest {
    embedRequest?: EmbedRequest;
}
export interface GetModelRequest {
    modelName: string;
}
export interface ListModelsRequest {
    type?: string;
    vectorType?: string;
}
export interface RerankOperationRequest {
    rerankRequest?: RerankRequest;
}
/**
 *
 */
export declare class InferenceApi extends runtime.BaseAPI {
    /**
     * Generate vector embeddings for input data. This endpoint uses Pinecone\'s [hosted embedding models](https://docs.pinecone.io/guides/index-data/create-an-index#embedding-models).
     * Generate vectors
     */
    embedRaw(requestParameters: EmbedOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmbeddingsList>>;
    /**
     * Generate vector embeddings for input data. This endpoint uses Pinecone\'s [hosted embedding models](https://docs.pinecone.io/guides/index-data/create-an-index#embedding-models).
     * Generate vectors
     */
    embed(requestParameters?: EmbedOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmbeddingsList>;
    /**
     * Get a description of a model hosted by Pinecone.   You can use hosted models as an integrated part of Pinecone operations or for standalone embedding and reranking. For more details, see [Vector embedding](https://docs.pinecone.io/guides/index-data/indexing-overview#vector-embedding) and [Rerank results](https://docs.pinecone.io/guides/search/rerank-results).
     * Describe a model
     */
    getModelRaw(requestParameters: GetModelRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ModelInfo>>;
    /**
     * Get a description of a model hosted by Pinecone.   You can use hosted models as an integrated part of Pinecone operations or for standalone embedding and reranking. For more details, see [Vector embedding](https://docs.pinecone.io/guides/index-data/indexing-overview#vector-embedding) and [Rerank results](https://docs.pinecone.io/guides/search/rerank-results).
     * Describe a model
     */
    getModel(requestParameters: GetModelRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ModelInfo>;
    /**
     * List the embedding and reranking models hosted by Pinecone.   You can use hosted models as an integrated part of Pinecone operations or for standalone embedding and reranking. For more details, see [Vector embedding](https://docs.pinecone.io/guides/index-data/indexing-overview#vector-embedding) and [Rerank results](https://docs.pinecone.io/guides/search/rerank-results).
     * List available models
     */
    listModelsRaw(requestParameters: ListModelsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ModelInfoList>>;
    /**
     * List the embedding and reranking models hosted by Pinecone.   You can use hosted models as an integrated part of Pinecone operations or for standalone embedding and reranking. For more details, see [Vector embedding](https://docs.pinecone.io/guides/index-data/indexing-overview#vector-embedding) and [Rerank results](https://docs.pinecone.io/guides/search/rerank-results).
     * List available models
     */
    listModels(requestParameters?: ListModelsRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ModelInfoList>;
    /**
     * Rerank results according to their relevance to a query.  For guidance and examples, see [Rerank results](https://docs.pinecone.io/guides/search/rerank-results).
     * Rerank documents
     */
    rerankRaw(requestParameters: RerankOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<RerankResult>>;
    /**
     * Rerank results according to their relevance to a query.  For guidance and examples, see [Rerank results](https://docs.pinecone.io/guides/search/rerank-results).
     * Rerank documents
     */
    rerank(requestParameters?: RerankOperationRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<RerankResult>;
}
