/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * The default value for the parameter when a parameter is optional.
 * @export
 * @interface ModelInfoSupportedParameterDefault
 */
export interface ModelInfoSupportedParameterDefault {
}
/**
 * Check if a given object implements the ModelInfoSupportedParameterDefault interface.
 */
export declare function instanceOfModelInfoSupportedParameterDefault(value: object): boolean;
export declare function ModelInfoSupportedParameterDefaultFromJSON(json: any): ModelInfoSupportedParameterDefault;
export declare function ModelInfoSupportedParameterDefaultFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModelInfoSupportedParameterDefault;
export declare function ModelInfoSupportedParameterDefaultToJSON(value?: ModelInfoSupportedParameterDefault | null): any;
