/**
 * Pinecone Data Plane API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { SparseValues } from './SparseValues';
/**
 *
 * @export
 * @interface Vector
 */
export interface Vector {
    /**
     * This is the vector's unique id.
     * @type {string}
     * @memberof Vector
     */
    id: string;
    /**
     * This is the vector data included in the request.
     * @type {Array<number>}
     * @memberof Vector
     */
    values?: Array<number>;
    /**
     *
     * @type {SparseValues}
     * @memberof Vector
     */
    sparseValues?: SparseValues;
    /**
     * This is the metadata included in the request.
     * @type {object}
     * @memberof Vector
     */
    metadata?: object;
}
/**
 * Check if a given object implements the Vector interface.
 */
export declare function instanceOfVector(value: object): boolean;
export declare function VectorFromJSON(json: any): Vector;
export declare function VectorFromJSONTyped(json: any, ignoreDiscriminator: boolean): Vector;
export declare function VectorToJSON(value?: Vector | null): any;
