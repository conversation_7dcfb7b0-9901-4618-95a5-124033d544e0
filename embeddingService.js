import { GoogleGenAI } from "@google/genai"; 
import dotenv from 'dotenv';
dotenv.config();

const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;

if (!GOOGLE_API_KEY) {
  console.error("CRITICAL: GOOGLE_API_KEY is not set in environment variables. Embedding service will not work.");
}

let genAI; // Will store the GoogleGenAI client instance

try {
  if (GOOGLE_API_KEY) {
    genAI = new GoogleGenAI({ apiKey: GOOGLE_API_KEY }); 
    console.log("GoogleGenAI client initialized for embedding service.");
  } else {
    console.warn("CRITICAL: Embedding service could not be initialized due to missing GOOGLE_API_KEY.");
  }
} catch (error) {
  console.error("CRITICAL FAILURE initializing GoogleGenAI client for Embedding Service:", error); 
  // genAI will remain undefined
}

export async function generateEmbedding(text) {
  if (!genAI) {
    console.error("generateEmbedding: GoogleGenAI client is not initialized. This indicates a startup problem.");
    throw new Error("GoogleGenAI client is not initialized. Check server startup logs for initialization errors.");
  }

  if (!text || typeof text !== 'string' || text.trim() === "") {
    console.warn("generateEmbedding called with empty or invalid text.");
    throw new Error("Cannot generate embedding for empty or invalid text.");
  }

  try {
    // Following user's example structure: ai.models.embedContent({ model: "...", contents: [...] })
    const result = await genAI.models.embedContent({
      model: "models/text-embedding-004", // Change to this model
      contents: [text]
    });
    
    if (result && result.embeddings && result.embeddings.length > 0 && result.embeddings[0].values) {
      return result.embeddings[0].values;
    } else {
      console.error("Embedding result from model is invalid or does not contain expected structure (result.embeddings[0].values):", result);
      throw new Error("Failed to generate valid embedding structure from model.");
    }
  } catch (error) {
    console.error("Error during genAI.models.embedContent call:", error);
    // Log specific details if available from the error object
    if (error.message && error.message.includes("token limit")) {
        console.error(`Input text (length: ${text.length}) likely exceeded the 2048 token limit for the embedding model.`);
    } else if (error.message && error.message.includes("API key not valid")) {
        console.error("The GOOGLE_API_KEY is likely invalid or missing required permissions.");
    }
    throw error; // Re-throw the error to be handled by the caller
  }
}
