/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ModelInfoMetric } from './ModelInfoMetric';
import type { ModelInfoSupportedParameter } from './ModelInfoSupportedParameter';
/**
 * Represents the model configuration including model type, supported parameters, and other model details.
 * @export
 * @interface ModelInfo
 */
export interface ModelInfo {
    /**
     * The name of the model.
     * @type {string}
     * @memberof ModelInfo
     */
    model: string;
    /**
     * A summary of the model.
     * @type {string}
     * @memberof ModelInfo
     */
    shortDescription: string;
    /**
     * The type of model (e.g. 'embed' or 'rerank').
     * @type {string}
     * @memberof ModelInfo
     */
    type: string;
    /**
     * Whether the embedding model produces 'dense' or 'sparse' embeddings.
     * @type {string}
     * @memberof ModelInfo
     */
    vectorType?: string;
    /**
     * The default embedding model dimension (applies to dense embedding models only).
     * @type {number}
     * @memberof ModelInfo
     */
    defaultDimension?: number;
    /**
     * The modality of the model (e.g. 'text').
     * @type {string}
     * @memberof ModelInfo
     */
    modality?: string;
    /**
     * The maximum tokens per sequence supported by the model.
     * @type {number}
     * @memberof ModelInfo
     */
    maxSequenceLength?: number;
    /**
     * The maximum batch size (number of sequences) supported by the model.
     * @type {number}
     * @memberof ModelInfo
     */
    maxBatchSize?: number;
    /**
     * The name of the provider of the model.
     * @type {string}
     * @memberof ModelInfo
     */
    providerName?: string;
    /**
     * The list of supported dimensions for the model (applies to dense embedding models only).
     * @type {Array<number>}
     * @memberof ModelInfo
     */
    supportedDimensions?: Array<number>;
    /**
     * The distance metrics supported by the model for similarity search.
     * @type {Array<ModelInfoMetric>}
     * @memberof ModelInfo
     */
    supportedMetrics?: Array<ModelInfoMetric>;
    /**
     *
     * @type {Array<ModelInfoSupportedParameter>}
     * @memberof ModelInfo
     */
    supportedParameters: Array<ModelInfoSupportedParameter>;
}
/**
 * Check if a given object implements the ModelInfo interface.
 */
export declare function instanceOfModelInfo(value: object): boolean;
export declare function ModelInfoFromJSON(json: any): ModelInfo;
export declare function ModelInfoFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModelInfo;
export declare function ModelInfoToJSON(value?: ModelInfo | null): any;
