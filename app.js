import express from 'express';
import cors from 'cors';
import mongoose from 'mongoose';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Import route handlers
import agentRoutes from './routes/agent-simplified.js';
import nluRoutes from './routes/nlu.js';
import multer from 'multer'; // Import multer
import fs from 'fs'; // Import fs for directory creation

// Load environment variables
dotenv.config();

// Debug environment variables
console.log('Environment variables loaded:');
console.log('GOOGLE_API_KEY:', process.env.GOOGLE_API_KEY ? 'Set' : 'Not set');

// Get the directory name (for ES modules)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure uploads directory exists
const uploadsDir = path.join(__dirname, 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Multer setup for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // Keep original filename, but add a timestamp to avoid overwrites if needed, or use a UUID
    // For simplicity, using original name + timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});
const upload = multer({ storage: storage });

// Create Express app
const app = express();
const PORT = process.env.PORT || 3082;

// Middleware
app.use(cors());
app.use(express.json({ limit: '30mb' })); // Increased limit for JSON payloads
app.use(express.urlencoded({ extended: true, limit: '30mb' })); // Also for URL-encoded, just in case

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Serve generated images
app.use('/generated-images', express.static(path.join(__dirname, 'generated-images')));

// Connect to MongoDB
const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;
if (mongoUri) {
  mongoose.connect(mongoUri)
    .then(() => console.log('Connected to MongoDB'))
    .catch(err => console.error('MongoDB connection error:', err));
} else {
  console.log('MongoDB URI not provided, running without database');
}

// Routes
app.use('/api/agent', agentRoutes);
app.use('/api/nlu', nluRoutes);

// File upload route
app.post('/api/upload/document', upload.single('document'), (req, res) => {
  if (!req.file) {
    return res.status(400).send({ error: 'No file uploaded.' });
  }
  // Return the path of the uploaded file
  // The path should be relative to the server's perspective or an absolute path
  // that the documentTool can access.
  // For documentTool, an absolute path is generally safer.
  const filePath = path.join(uploadsDir, req.file.filename);
  res.send({ filePath: filePath, originalName: req.file.originalname });
});

// Root route - serve the main HTML page (Varjis UI)
app.get('/', (_req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`Voice Agent server running on http://localhost:${PORT}`);
  console.log(`Using simplified single-persona mode with en-IN-Wavenet-F voice`);
});
