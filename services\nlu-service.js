/**
 * NLU Service
 *
 * Provides natural language understanding capabilities using Google's Generative AI
 */

import { GoogleGenAI } from '@google/genai';
import dotenv from 'dotenv';

dotenv.config();

// Configuration for the NLU model
const llmNluConfig = {
  modelName: "gemma-3-4b-it", // Updated to user-specified model
  generationConfig: {
    temperature: 0.3, // Lower temperature for more deterministic NLU output
    topK: 20,
    topP: 0.8,
    maxOutputTokens: 512, // Increased for potential explanations
  },
};

// Initialize the Google AI model for NLU
const ai = new GoogleGenAI({apiKey: process.env.GOOGLE_API_KEY});

// Define custom intents - expand this list as needed
const customIntents = [
  // Original intents
  "book_flight",
  "get_weather",
  "play_music",
  "set_reminder",
  "get_news",
  "query_time",
  "translate",
  "express_positive_emotion",
  "express_dissatisfaction",
  "provide_feedback",
  "set_alarm",
  "request_information",
  "make_a_reservation",
  "order_food",

  // Email-related intents
  "check_email", // General check
  "list_emails", // More specific for retrieving lists of emails
  "retrieve_email_content", // For getting specific email details (could be a future refinement)
  "send_email",
  "summarize_emails",
  "filter_emails",

  // Web-related intents
  "search_web",
  "open_website",
  "get_directions",

  // Utility intents
  "calculate",
  "convert_units",
  "define_term",

  // Device control intents
  "adjust_volume",
  "change_settings",

  // Personal assistant intents
  "tell_joke",
  "get_recommendations",
  "create_list",
  "add_to_list",

  // Conversation flow intents
  "greeting",
  "farewell",
  "thank_you",
  "repeat_information",
  "clarify_information",

  // Fallback intent
  "unknown"
];

// Construct the intent instructions string for the prompt
const intentInstructions = `The possible intents are: ${customIntents.join(", ")}.`;

// The prompt template
const nluPromptTemplate = `You are a detailed linguistic analysis engine. Your task is to read the user's message and accurately determine its sentiment, the primary intent (from the provided list), and identify any relevant named entities with as much specificity as possible. Return your analysis as a JSON object with the following keys:
- "sentiment": The overall sentiment of the message ("positive", "negative", "neutral", or "mixed").
- "intent": A concise label describing the user's primary goal or request from the following list: ${intentInstructions}. If the user is asking to see, get, fetch, check, or retrieve their emails, use the "list_emails" intent. If the intent is unclear, use "unknown".
- "entities": An object where keys are the entity types (e.g., "location", "date", "product", "person", "team", "email_filter", "email_count") and values are arrays of the identified entities. Be specific (e.g., "Winter Haven, Florida").
- "confidence": An object containing your confidence score (between 0 and 1) for each of "sentiment", "intent", and "entities".
- "explanation": A brief explanation for the confidence score assigned to each of "sentiment", "intent", and "entities".
Consider the current context: Current location is Winter Haven, Florida, United States; Current time is {{current_time}}.
Analyze the following user message:
""" {{user_message}} """
Respond ONLY with a valid JSON object.`;

/**
 * Analyze a user message using the NLU engine
 * @param {string} userMessage - The message to analyze
 * @param {Object} context - Additional context for the NLU analysis (e.g., { location: "San Francisco" })
 * @param {Array} conversationHistory - An array of previous conversation turns
 * @returns {Promise<Object>} - An object containing { nluResult, conversationHistory }
 */
export async function analyze(userMessage, context = {}, conversationHistory = []) {
    // Get current time for context
    const now = new Date();
    const timeString = now.toLocaleString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        timeZoneName: 'short'
    });

    // Construct the prompt with conversation history and context
    const historyString = conversationHistory.map((turn, index) => `Turn ${index + 1}: User: ${turn.user}, Assistant: ${turn.assistant}`).join('\n');
    const contextString = Object.entries(context).map(([key, value]) => `${key}: ${value}`).join(', ');

    // Modify the prompt template.
    // The original template has: "Consider the current context: Current location is Winter Haven, Florida, United States; Current time is {{current_time}}."
    // We will replace "{{current_time}}", then the static location, then prepend other context and history.
    let prompt = nluPromptTemplate
        .replace("{{user_message}}", userMessage)
        .replace("{{current_time}}", timeString)
        .replace("Current location is Winter Haven, Florida, United States", `Current Location is ${context.location || 'Winter Haven, Florida, United States'}`);
    
    // Inject additional context and history before the "Analyze the following user message:" line
    // To do this robustly, we find a known part of the template and insert before it.
    // A good spot is before "Analyze the following user message:"
    // Or, more simply, modify the "Consider the current context:" line.
    // The existing template line: "Consider the current context: Current location is Winter Haven, Florida, United States; Current time is {{current_time}}."
    // After the above replacements, it becomes something like: "Consider the current context: Current Location is [dynamic location]; Current time is [dynamic time]."
    // We can augment this line.
    const baseContextLine = `Current Location is ${context.location || 'Winter Haven, Florida, United States'}; Current time is ${timeString}`;
    const enhancedContext = `Consider the current context:\n${contextString ? contextString + '\n' : ''}Conversation History:\n${historyString ? historyString + '\n' : ''}${baseContextLine.replace("Consider the current context: ", "")}`;

    prompt = prompt.replace(
      `Current Location is ${context.location || 'Winter Haven, Florida, United States'}; Current time is ${timeString}`, // This is what the line becomes after initial replacements
      enhancedContext // Replace it with the fully enhanced context string
    );
    
    // If contextString or historyString are empty, we might have double newlines. Clean up.
    prompt = prompt.replace(/\n\n/g, '\n');


    try {
        const result = await ai.models.generateContent({
            model: llmNluConfig.modelName,
            contents: prompt, // Using 'prompt' directly as per existing file structure
            generationConfig: llmNluConfig.generationConfig,
        });
        
        const responseText = result.text; // Aligning with existing file structure

        if (responseText) {
            let jsonString = responseText.trim();

            if (jsonString.startsWith("```")) {
                jsonString = jsonString.replace(/^```(?:json)?/, '').replace(/```$/, '').trim();
            }

            let nluResult = null;
            try {
                nluResult = JSON.parse(jsonString);
            } catch (error) {
                console.error("Primary JSON parse failed. Attempting fallback extraction.");
                const firstBrace = jsonString.indexOf('{');
                const lastBrace = jsonString.lastIndexOf('}');
                if (firstBrace !== -1 && lastBrace !== -1) {
                    const fallback = jsonString.substring(firstBrace, lastBrace + 1);
                    try {
                        nluResult = JSON.parse(fallback);
                    } catch (fallbackError) {
                        console.error("Fallback parse also failed:", fallbackError, fallback);
                        // nluResult remains null
                    }
                }
            }

            // NLU conversation history is separate from LLM conversation history
            let updatedConversationHistory = [...conversationHistory];
            if (nluResult) {
                // Only store user messages for NLU context - don't pollute LLM history
                updatedConversationHistory.push({ user: userMessage });
                if (updatedConversationHistory.length > 3) {
                    updatedConversationHistory = updatedConversationHistory.slice(1);
                }
            }
            return { nluResult, conversationHistory: updatedConversationHistory };

        } else {
            console.warn("Gemini NLU returned an empty response.");
            return { nluResult: null, conversationHistory }; // Return original history
        }
    } catch (error) {
        console.error("Error calling Gemini for NLU:", error);
        return { nluResult: null, conversationHistory }; // Return original history
    }
}

