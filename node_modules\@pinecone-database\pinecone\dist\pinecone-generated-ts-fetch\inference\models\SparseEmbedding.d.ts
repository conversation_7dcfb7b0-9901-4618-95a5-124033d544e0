/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * A sparse embedding of a single input
 * @export
 * @interface SparseEmbedding
 */
export interface SparseEmbedding {
    /**
     * The sparse embedding values.
     * @type {Array<number>}
     * @memberof SparseEmbedding
     */
    sparseValues: Array<number>;
    /**
     * The sparse embedding indices.
     * @type {Array<number>}
     * @memberof SparseEmbedding
     */
    sparseIndices: Array<number>;
    /**
     * The normalized tokens used to create the sparse embedding.
     * @type {Array<string>}
     * @memberof SparseEmbedding
     */
    sparseTokens?: Array<string>;
    /**
     * Indicates whether this is a 'dense' or 'sparse' embedding.
     * @type {string}
     * @memberof SparseEmbedding
     */
    vectorType: string;
}
/**
 * Check if a given object implements the SparseEmbedding interface.
 */
export declare function instanceOfSparseEmbedding(value: object): boolean;
export declare function SparseEmbeddingFromJSON(json: any): SparseEmbedding;
export declare function SparseEmbeddingFromJSONTyped(json: any, ignoreDiscriminator: boolean): SparseEmbedding;
export declare function SparseEmbeddingToJSON(value?: SparseEmbedding | null): any;
