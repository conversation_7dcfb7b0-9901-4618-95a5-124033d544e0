/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { RankedDocument } from './RankedDocument';
import type { RerankResultUsage } from './RerankResultUsage';
/**
 * The result of a reranking request.
 * @export
 * @interface RerankResult
 */
export interface RerankResult {
    /**
     * The model used to rerank documents.
     * @type {string}
     * @memberof RerankResult
     */
    model: string;
    /**
     * The reranked documents.
     * @type {Array<RankedDocument>}
     * @memberof RerankResult
     */
    data: Array<RankedDocument>;
    /**
     *
     * @type {RerankResultUsage}
     * @memberof RerankResult
     */
    usage: RerankResultUsage;
}
/**
 * Check if a given object implements the RerankResult interface.
 */
export declare function instanceOfRerankResult(value: object): boolean;
export declare function RerankResultFromJSON(json: any): RerankResult;
export declare function RerankResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): RerankResult;
export declare function RerankResultToJSON(value?: RerankResult | null): any;
