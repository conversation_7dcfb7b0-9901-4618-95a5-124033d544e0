/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 *
 * @export
 * @interface ModelInfoSupportedParameterAllowedValuesInner
 */
export interface ModelInfoSupportedParameterAllowedValuesInner {
}
/**
 * Check if a given object implements the ModelInfoSupportedParameterAllowedValuesInner interface.
 */
export declare function instanceOfModelInfoSupportedParameterAllowedValuesInner(value: object): boolean;
export declare function ModelInfoSupportedParameterAllowedValuesInnerFromJSON(json: any): ModelInfoSupportedParameterAllowedValuesInner;
export declare function ModelInfoSupportedParameterAllowedValuesInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModelInfoSupportedParameterAllowedValuesInner;
export declare function ModelInfoSupportedParameterAllowedValuesInnerToJSON(value?: ModelInfoSupportedParameterAllowedValuesInner | null): any;
