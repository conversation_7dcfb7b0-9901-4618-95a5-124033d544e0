/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/**
 * A distance metric that the embedding model supports for similarity searches.
 * @export
 */
export declare const ModelInfoMetric: {
    readonly Cosine: "cosine";
    readonly Euclidean: "euclidean";
    readonly Dotproduct: "dotproduct";
};
export type ModelInfoMetric = typeof ModelInfoMetric[keyof typeof ModelInfoMetric];
export declare function ModelInfoMetricFromJSON(json: any): ModelInfoMetric;
export declare function ModelInfoMetricFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModelInfoMetric;
export declare function ModelInfoMetricToJSON(value?: ModelInfoMetric | null): any;
