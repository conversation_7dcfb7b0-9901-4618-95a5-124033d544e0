/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ModelInfoSupportedParameterAllowedValuesInner } from './ModelInfoSupportedParameterAllowedValuesInner';
import type { ModelInfoSupportedParameterDefault } from './ModelInfoSupportedParameterDefault';
/**
 * Describes a parameter supported by the model, including parameter value constraints.
 * @export
 * @interface ModelInfoSupportedParameter
 */
export interface ModelInfoSupportedParameter {
    /**
     * The name of the parameter.
     * @type {string}
     * @memberof ModelInfoSupportedParameter
     */
    parameter: string;
    /**
     * The parameter type e.g. 'one_of', 'numeric_range', or 'any'.
     *
     * If the type is 'one_of', then 'allowed_values' will be set, and the value specified must be one of the allowed values. 'one_of' is only compatible with value_type 'string' or 'integer'.
     *
     * If 'numeric_range', then 'min' and 'max' will be set, then the value specified must adhere to the value_type and must fall within the `[min, max]` range (inclusive).
     *
     * If 'any' then any value is allowed, as long as it adheres to the value_type.
     * @type {string}
     * @memberof ModelInfoSupportedParameter
     */
    type: string;
    /**
     * The type of value the parameter accepts, e.g. 'string', 'integer', 'float', or 'boolean'.
     * @type {string}
     * @memberof ModelInfoSupportedParameter
     */
    valueType: string;
    /**
     * Whether the parameter is required (true) or optional (false).
     * @type {boolean}
     * @memberof ModelInfoSupportedParameter
     */
    required: boolean;
    /**
     * The allowed parameter values when the type is 'one_of'.
     * @type {Array<ModelInfoSupportedParameterAllowedValuesInner>}
     * @memberof ModelInfoSupportedParameter
     */
    allowedValues?: Array<ModelInfoSupportedParameterAllowedValuesInner>;
    /**
     * The minimum allowed value (inclusive) when the type is 'numeric_range'.
     * @type {number}
     * @memberof ModelInfoSupportedParameter
     */
    min?: number;
    /**
     * The maximum allowed value (inclusive) when the type is 'numeric_range'.
     * @type {number}
     * @memberof ModelInfoSupportedParameter
     */
    max?: number;
    /**
     *
     * @type {ModelInfoSupportedParameterDefault}
     * @memberof ModelInfoSupportedParameter
     */
    _default?: ModelInfoSupportedParameterDefault;
}
/**
 * Check if a given object implements the ModelInfoSupportedParameter interface.
 */
export declare function instanceOfModelInfoSupportedParameter(value: object): boolean;
export declare function ModelInfoSupportedParameterFromJSON(json: any): ModelInfoSupportedParameter;
export declare function ModelInfoSupportedParameterFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModelInfoSupportedParameter;
export declare function ModelInfoSupportedParameterToJSON(value?: ModelInfoSupportedParameter | null): any;
