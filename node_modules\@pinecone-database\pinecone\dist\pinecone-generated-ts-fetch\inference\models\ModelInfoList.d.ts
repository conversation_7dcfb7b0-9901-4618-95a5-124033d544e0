/**
 * Pinecone Inference API
 * Pinecone is a vector database that makes it easy to search and retrieve billions of high-dimensional vectors.
 *
 * The version of the OpenAPI document: 2025-04
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import type { ModelInfo } from './ModelInfo';
/**
 * The list of available models.
 * @export
 * @interface ModelInfoList
 */
export interface ModelInfoList {
    /**
     *
     * @type {Array<ModelInfo>}
     * @memberof ModelInfoList
     */
    models?: Array<ModelInfo>;
}
/**
 * Check if a given object implements the ModelInfoList interface.
 */
export declare function instanceOfModelInfoList(value: object): boolean;
export declare function ModelInfoListFromJSON(json: any): ModelInfoList;
export declare function ModelInfoListFromJSONTyped(json: any, ignoreDiscriminator: boolean): ModelInfoList;
export declare function ModelInfoListToJSON(value?: ModelInfoList | null): any;
