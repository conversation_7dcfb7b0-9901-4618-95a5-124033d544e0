/**
 * Agent Logic Service (Refactored to use Agent Class)
 *
 * This service now acts as a thin wrapper to initialize and expose
 * a default Agent instance (e.g., Varjis).
 */

import Agent from './services/Agent.js';
import Persona from './Persona.js';
import LLMClient from './LLMClient.js';
import { HybridConversationMemory } from './memory.js';
import kbToolInstance from './tools/kbTool.js'; // Import KnowledgeBaseTool

// Default Agent (Varjis)
let defaultAgent = null;

const defaultAgentGenerationConfig = {
  temperature: 0.7,
  topK: 40,
  topP: 0.95,
  maxOutputTokens: 4096,
};

// Initialize the default agent asynchronously
(async () => {
  try {
    // Ensure GOOGLE_API_KEY is available
    if (!process.env.GOOGLE_API_KEY) {
      throw new Error("GOOGLE_API_KEY environment variable is not set. Please ensure it is defined in your .env file.");
    }

    const varjisPersona = await Persona.load('varjis_llm');
    const llmClient = new LLMClient({
      apiKey: process.env.GOOGLE_API_KEY,
      generationConfig: defaultAgentGenerationConfig
    });
    const conversationMemory = new HybridConversationMemory({
      maxSizeBytes: 5 * 1024 * 1024, // 5MB
      maxMessageCount: 10, // Reduced for RAG - Pinecone handles long-term
      sessionId: 'default-varjis-session' // Unique session ID for the default agent
    });

    defaultAgent = new Agent({
      persona: varjisPersona,
      llmClient: llmClient,
      memory: conversationMemory,
      agentGenerationConfig: defaultAgentGenerationConfig
    });

    // Add DateTimeTool to the default agent
    // const dateTimeToolInstance = new DateTimeTool();
    // defaultAgent.addTool(dateTimeToolInstance.name, dateTimeToolInstance); // Use tool's own name property
    // console.log(`Tool '${dateTimeToolInstance.name}' added to agent "${varjisPersona.name}".`);

    // Add SearchTool to the default agent
    // defaultAgent.addTool(searchTool.name, searchTool); // Use tool's own name property
    // console.log(`Tool '${searchTool.name}' added to agent "${varjisPersona.name}".`);

    // Add DescribeImageTool to the default agent
    // It will use GOOGLE_API_KEY from process.env by default if not overridden by GEMINI_API_KEY
    // const describeImageToolInstance = new DescribeImageTool(process.env.GOOGLE_API_KEY); 
    // defaultAgent.addTool(describeImageToolInstance.name, describeImageToolInstance);
    // console.log(`Tool '${describeImageToolInstance.name}' added to agent "${varjisPersona.name}".`);

    // // Add CalculatorTool to the default agent
    // defaultAgent.addTool(calculatorTool.name, calculatorTool); // Use tool's own name property
    // console.log(`Tool '${calculatorTool.name}' added to agent "${varjisPersona.name}".`);

    // // Add BrowserTool to the default agent
    // defaultAgent.addTool(browserTool.name, browserTool); // Use tool's own name property
    // console.log(`Tool '${browserTool.name}' added to agent "${varjisPersona.name}".`);

    // // Add ImgGenTool to the default agent
    // // imgGenTool is a function, so we'll assign a name.
    // // It doesn't have a .name property like class-based tools.
    // defaultAgent.addTool('imgGenTool', imgGenTool);
    // console.log(`Tool 'imgGenTool' added to agent "${varjisPersona.name}".`);

    // // Add DocumentTool to the default agent
    // defaultAgent.addTool('documentTool', documentTool); // documentTool is a function
    // console.log(`Tool 'documentTool' added to agent "${varjisPersona.name}".`);

    // // Add VideoTool to the default agent
    // defaultAgent.addTool('videoTool', videoTool); // videoTool is a function
    // console.log(`Tool 'videoTool' added to agent "${varjisPersona.name}".`);

    // // Add EmailTool to the default agent
    // defaultAgent.addTool(emailTool.name, emailTool); // emailTool is an instance with a .name property
    // console.log(`Tool '${emailTool.name}' added to agent "${varjisPersona.name}".`);

    // // Add KnowledgeBaseTool to the default agent
    // defaultAgent.addTool(kbToolInstance.name, kbToolInstance);
    // console.log(`Tool '${kbToolInstance.name}' added to agent "${varjisPersona.name}".`);

    console.log(`Default agent "${varjisPersona.name}" initialized successfully with all tools.`);

  } catch (error) {
    console.error('FATAL: Error initializing default agent:', error);
    // For now, defaultAgent will remain null.
    // A more robust application might stop or enter a degraded mode.
  }
})();

/**
 * Function to clean the AI's text response for better TTS.
 * This function is exported as Agent.js imports it.
 * @param {string} text - The AI's raw text response.
 * @returns {string} - The cleaned text.
 */
export function cleanTextForTTS(text) {
  // Implement cleaning logic for better speech output
  let cleanedText = text;

  // Remove markdown formatting
  cleanedText = cleanedText.replace(/\[.*?\]\(.*?\)/g, ''); // Remove markdown links
  cleanedText = cleanedText.replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold markdown
  cleanedText = cleanedText.replace(/\_(.*?)\_/g, '$1');   // Remove italic markdown
  cleanedText = cleanedText.replace(/\`(.*?)\`/g, '$1');   // Remove code formatting

  // Improve pronunciation of technical terms
  cleanedText = cleanedText.replace(/n8n/gi, 'N eight N'); // Pronunciation help
  cleanedText = cleanedText.replace(/Make \(formerly Integromat\)/gi, 'Make, formerly known as Integromat');

  // Clean up URLs for better speech
  cleanedText = cleanedText.replace(/https?:\/\/[^\s)]+/g, 'link'); // Replace URLs with 'link'
  cleanedText = cleanedText.replace(/Source \d+: link/g, ''); // Remove source links

  // Clean up search result formatting
  cleanedText = cleanedText.replace(/--- Content from .* ---/g, 'According to this source:');
  cleanedText = cleanedText.replace(/Source: .*/g, '');

  // Remove any remaining special characters that might affect speech
  cleanedText = cleanedText.replace(/\|/g, ', '); // Replace pipes with commas
  cleanedText = cleanedText.replace(/\\n/g, ' '); // Replace literal \n with space
  cleanedText = cleanedText.replace(/\\t/g, ' '); // Replace literal \t with space

  // Fix common abbreviations for better speech
  cleanedText = cleanedText.replace(/vs\./g, 'versus');
  cleanedText = cleanedText.replace(/e\.g\./g, 'for example');
  cleanedText = cleanedText.replace(/i\.e\./g, 'that is');

  // More aggressive cleaning to prevent TTS issues
  // Remove all non-alphanumeric characters except basic punctuation
  cleanedText = cleanedText.replace(/[^a-zA-Z0-9\s.,;:?!'"()-]/g, ' ');

  // Fix multiple spaces
  cleanedText = cleanedText.replace(/\s+/g, ' ');

  // Remove spaces before punctuation
  cleanedText = cleanedText.replace(/\s+([.,;:?!])/g, '$1');

  // Ensure the text ends with proper punctuation
  if (!/[.!?]\s*$/.test(cleanedText)) {
    cleanedText = cleanedText.trim() + '.';
  }

  return cleanedText;
}

/**
 * Process a message using the default AI agent.
 * @param {string} message - The user's message.
 * @param {object} [context={}] - Additional context for processing.
 * @returns {Promise<object>} - The agent's response.
 */
export async function processMessage(message, context = {}) {
  if (!defaultAgent) {
    console.warn('Default agent not yet initialized, waiting briefly for initialization...');
    await new Promise(resolve => setTimeout(resolve, 500)); // Wait 0.5 seconds
    if (!defaultAgent) {
        console.error('FATAL: Default agent is still not initialized after waiting. Cannot process message.');
        return {
            text: "I'm sorry, the agent is currently unavailable. Please try again shortly.",
            cleanedText: "I'm sorry, the agent is currently unavailable. Please try again shortly.",
            audioBuffer: null,
            audioUrl: null,
            memorySize: 0,
            error: true,
            errorCode: 'AGENT_NOT_INITIALIZED'
        };
    }
  }
  return defaultAgent.processMessage(message, context);
}

/**
 * Get the voice settings from the default AI agent.
 * @returns {Promise<object>} - The voice settings.
 */
export async function getVoiceSettings() {
  if (!defaultAgent) {
    console.warn('Default agent not yet initialized for getVoiceSettings, waiting briefly...');
    await new Promise(resolve => setTimeout(resolve, 500)); // Wait 0.5 seconds
    if (!defaultAgent) {
        console.error('FATAL: Default agent is still not initialized after waiting. Cannot get voice settings.');
        return { // Sensible fallback voice settings
            languageCode: "en-US",
            voiceName: "en-US-Standard-C",
            ssmlGender: "NEUTRAL",
            speakingRate: 1.0,
            pitch: 0.0
        };
    }
  }
  return defaultAgent.getVoiceSettings();
}

export default {
  processMessage,
  getVoiceSettings,
  cleanTextForTTS
};
